/// 健康目标数据模型
/// 用于存储用户的健康目标设定信息
class HealthGoal {
  final int? id;
  final DateTime recordDate;
  final String? goalTypes; // 目标类型，逗号分隔的字符串
  final double? targetWeight; // 目标体重
  final int? targetDays; // 目标期限天数
  final String? activityLevel; // 活动水平
  final DateTime? createdTime;

  HealthGoal({
    this.id,
    required this.recordDate,
    this.goalTypes,
    this.targetWeight,
    this.targetDays,
    this.activityLevel,
    this.createdTime,
  });

  /// 从Map创建HealthGoal对象（用于从数据库读取）
  factory HealthGoal.fromMap(Map<String, dynamic> map) {
    return HealthGoal(
      id: map['id'],
      recordDate: DateTime.parse(map['record_date']),
      goalTypes: map['goal_types'],
      targetWeight: map['target_weight'],
      targetDays: map['target_days'],
      activityLevel: map['activity_level'],
      createdTime: map['created_time'] != null ? DateTime.parse(map['created_time']) : null,
    );
  }

  /// 将HealthGoal对象转换为Map（用于写入数据库）
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'record_date': recordDate.toIso8601String(),
      if (goalTypes != null) 'goal_types': goalTypes,
      if (targetWeight != null) 'target_weight': targetWeight,
      if (targetDays != null) 'target_days': targetDays,
      if (activityLevel != null) 'activity_level': activityLevel,
    };
  }

  /// 复制对象并修改部分属性
  HealthGoal copyWith({
    int? id,
    DateTime? recordDate,
    String? goalTypes,
    double? targetWeight,
    int? targetDays,
    String? activityLevel,
    DateTime? createdTime,
  }) {
    return HealthGoal(
      id: id ?? this.id,
      recordDate: recordDate ?? this.recordDate,
      goalTypes: goalTypes ?? this.goalTypes,
      targetWeight: targetWeight ?? this.targetWeight,
      targetDays: targetDays ?? this.targetDays,
      activityLevel: activityLevel ?? this.activityLevel,
      createdTime: createdTime ?? this.createdTime,
    );
  }

  @override
  String toString() {
    return 'HealthGoal(id: $id, recordDate: $recordDate, goalTypes: $goalTypes, targetWeight: $targetWeight, targetDays: $targetDays, activityLevel: $activityLevel)';
  }
}
