import 'dart:convert';

import 'package:logger/logger.dart';

import '../../core/database_service.dart';
import '../../config/database_config.dart';
import '../base/base_dao.dart';
import '../../../../models/health_goal.dart';
import '../../../../../../core/utils/database_optimizer.dart';

final Logger logger = Logger();

/// 健康目标DAO
/// 负责健康目标的数据库操作
class HealthGoalDao extends BaseDaoImpl<HealthGoal> {
  final DatabaseService _dbService = DatabaseService();

  @override
  String get tableName => DatabaseConfig.tableHealthGoal;

  @override
  Map<String, dynamic> toMap(HealthGoal entity) => entity.toMap();

  @override
  HealthGoal fromMap(Map<String, dynamic> map) => HealthGoal.fromMap(map);

  @override
  int? getId(HealthGoal entity) => entity.id;

  @override
  HealthGoal setId(HealthGoal entity, int id) {
    return entity.copyWith(id: id);
  }

  @override
  Future<int> insert(HealthGoal entity) async {
    try {
      return await _dbService.insert(tableName, toMap(entity));
    } catch (e) {
      logger.e('添加健康目标记录失败: $e');
      rethrow;
    }
  }

  /// 添加健康目标记录（保持向后兼容）
  Future<int> addHealthGoal(HealthGoal healthGoal) => insert(healthGoal);

  @override
  Future<HealthGoal?> findById(int id) async {
    try {
      final result = await _dbService.findById(tableName, id);
      return result != null ? fromMap(result) : null;
    } catch (e) {
      logger.e('根据ID查询健康目标失败: $e');
      rethrow;
    }
  }

  /// 获取最新的健康目标记录
  Future<HealthGoal?> getLatestHealthGoal() async {
    const cacheKey = 'latest_health_goal';

    return await DatabaseOptimizer.cachedQuery(
      cacheKey,
      () async {
        try {
          DatabaseOptimizer.recordQuery('getLatestHealthGoal');
          final results = await _dbService.query(tableName, orderBy: 'record_date DESC', limit: 1);
          return results.isNotEmpty ? fromMap(results.first) : null;
        } catch (e) {
          logger.e('获取最新健康目标记录失败: $e');
          rethrow;
        }
      },
      cacheExpiry: const Duration(minutes: 10), // 缓存10分钟
    );
  }

  /// 获取指定日期的健康目标记录
  Future<HealthGoal?> getHealthGoalByDate(String date) async {
    try {
      final dateOnly = date.substring(0, 10); // 只取日期部分，不含时间
      final results = await _dbService.query(tableName, where: 'date(record_date) = ?', whereArgs: [dateOnly]);
      return results.isNotEmpty ? fromMap(results.first) : null;
    } catch (e) {
      logger.e('根据日期查询健康目标失败: $e');
      rethrow;
    }
  }

  @override
  Future<List<HealthGoal>> findAll() async {
    try {
      final results = await _dbService.query(tableName, orderBy: 'record_date DESC');
      return results.map((map) => fromMap(map)).toList();
    } catch (e) {
      logger.e('获取所有健康目标记录失败: $e');
      rethrow;
    }
  }

  /// 获取所有健康目标记录（保持向后兼容）
  Future<List<HealthGoal>> getAllHealthGoals() => findAll();

  @override
  Future<int> update(HealthGoal entity) async {
    try {
      final id = getId(entity);
      if (id == null) {
        throw Exception('更新健康目标记录失败: 缺少ID');
      }
      logger.i('更新健康目标记录: ${jsonEncode(toMap(entity))}');
      return await _dbService.updateById(tableName, toMap(entity), id);
    } catch (e) {
      logger.e('更新健康目标记录失败: $e');
      rethrow;
    }
  }

  /// 更新健康目标记录（保持向后兼容）
  Future<int> updateHealthGoal(HealthGoal healthGoal) => update(healthGoal);

  @override
  Future<int> deleteById(int id) async {
    try {
      final result = await _dbService.deleteById(tableName, id);
      if (result == 0) {
        logger.w('删除失败: 未找到ID为$id的健康目标记录');
      }
      return result;
    } catch (e) {
      logger.e('删除健康目标记录失败: $e');
      rethrow;
    }
  }

  /// 根据目标类型查询健康目标记录
  Future<List<HealthGoal>> getHealthGoalsByType(String goalType) async {
    try {
      final results = await _dbService.query(
        tableName,
        where: 'goal_types LIKE ?',
        whereArgs: ['%$goalType%'],
        orderBy: 'record_date DESC',
      );
      return results.map((map) => fromMap(map)).toList();
    } catch (e) {
      logger.e('根据目标类型查询健康目标失败: $e');
      rethrow;
    }
  }

  /// 获取指定日期范围内的健康目标记录
  Future<List<HealthGoal>> getHealthGoalsByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      final results = await _dbService.query(
        tableName,
        where: 'date(record_date) BETWEEN ? AND ?',
        whereArgs: [startDate.toIso8601String().substring(0, 10), endDate.toIso8601String().substring(0, 10)],
        orderBy: 'record_date DESC',
      );
      return results.map((map) => fromMap(map)).toList();
    } catch (e) {
      logger.e('根据日期范围查询健康目标失败: $e');
      rethrow;
    }
  }
}
