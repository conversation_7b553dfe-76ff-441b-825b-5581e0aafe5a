import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../../../core/services/http/http_api.dart';

/// 帮助与反馈控制器
class HelpFeedbackController extends GetxController {
  // 表单相关
  final formKey = GlobalKey<FormState>();
  final feedbackController = TextEditingController();
  final contactController = TextEditingController();

  // 反馈类型
  final RxString selectedFeedbackType = '功能建议'.obs;
  final List<String> feedbackTypes = ['功能建议', '问题反馈', '使用咨询', '其他'];

  // 常见问题数据
  final RxList<Map<String, String>> faqList =
      <Map<String, String>>[
        {'question': '如何记录饮食数据？', 'answer': '在首页点击"记录"按钮，选择食物类型，输入食物名称和重量，系统会自动计算卡路里。'},
        // {
        //   'question': '如何设置减重目标？',
        //   'answer': '进入"我的"页面，点击"目标设定"，根据个人情况设置目标体重和时间计划。'
        // },
        // {
        //   'question': '数据同步问题怎么办？',
        //   'answer': '请检查网络连接，确保登录状态正常。如问题持续存在，请联系客服。'
        // },
        // {
        //   'question': '如何修改个人信息？',
        //   'answer': '在"我的"页面点击头像或个人信息区域，即可进入个人信息编辑页面。'
        // },
        // {
        //   'question': '忘记密码怎么办？',
        //   'answer': '在登录页面点击"忘记密码"，输入注册邮箱或手机号，按提示重置密码。'
        // }
      ].obs;

  // 加载状态
  final RxBool isSubmitting = false.obs;

  // 展开状态管理
  final RxList<bool> expandedStates = <bool>[].obs;

  @override
  void onInit() {
    super.onInit();
    // 初始化展开状态
    expandedStates.value = List.generate(faqList.length, (index) => false);
  }

  @override
  void onClose() {
    feedbackController.dispose();
    contactController.dispose();
    super.onClose();
  }

  /// 切换FAQ展开状态
  void toggleFaqExpansion(int index) {
    expandedStates[index] = !expandedStates[index];
  }

  /// 更新反馈类型
  void updateFeedbackType(String type) {
    selectedFeedbackType.value = type;
  }

  /// 提交反馈
  Future<void> submitFeedback() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    try {
      isSubmitting.value = true;

      // 获取设备信息和应用版本
      final deviceInfo = await _getDeviceInfo();
      final appVersion = await _getAppVersion();

      // 构建反馈内容
      final feedbackContent = '''[${selectedFeedbackType.value}]${feedbackController.text}''';

      // 提交反馈到服务器
      final result = await HttpApi.submitFeedback(
        content: feedbackContent,
        contact: contactController.text.isNotEmpty ? contactController.text : null,
        deviceInfo: deviceInfo,
        appVersion: appVersion,
      );

      // 检查响应状态
      if (result.statusCode == 200) {
        // 显示成功消息
        Get.snackbar(
          '提交成功',
          '感谢您的反馈，我们会尽快处理！',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
          duration: const Duration(seconds: 3),
        );

        // 清空表单
        feedbackController.clear();
        contactController.clear();
        selectedFeedbackType.value = '功能建议';
      } else {
        throw Exception('服务器返回错误: ${result.statusCode}');
      }
    } catch (e) {
      Get.snackbar(
        '提交失败',
        '网络异常，请稍后重试: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    } finally {
      isSubmitting.value = false;
    }
  }

  /// 获取设备信息
  Future<String> _getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();

      if (GetPlatform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return '${androidInfo.brand} ${androidInfo.model} (Android ${androidInfo.version.release})';
      } else if (GetPlatform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return '${iosInfo.name} ${iosInfo.model} (iOS ${iosInfo.systemVersion})';
      } else if (GetPlatform.isMacOS) {
        final macInfo = await deviceInfo.macOsInfo;
        return '${macInfo.computerName} (macOS ${macInfo.osRelease})';
      } else {
        return '未知设备';
      }
    } catch (e) {
      return '设备信息获取失败';
    }
  }

  /// 获取应用版本
  Future<String> _getAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return '${packageInfo.version} (${packageInfo.buildNumber})';
    } catch (e) {
      return '版本信息获取失败';
    }
  }

  /// 联系客服
  void contactCustomerService() {
    Get.snackbar(
      '客服联系方式',
      '客服微信：BD6JDO\n工作时间：9:00-18:00',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.blue.shade100,
      colorText: Colors.blue.shade800,
      duration: const Duration(seconds: 5),
    );
  }
}
