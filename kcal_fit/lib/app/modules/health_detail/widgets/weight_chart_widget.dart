import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

/// 体重变化图表组件
class WeightChartWidget extends StatelessWidget {
  final List<Map<String, dynamic>> data;
  final Color color;

  const WeightChartWidget({super.key, required this.data, required this.color});

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return const Center(child: Text('暂无体重数据', style: TextStyle(color: Colors.grey, fontSize: 16)));
    }

    return _buildChart();
  }

  /// 构建图表
  Widget _buildChart() {
    final spots = _generateSpots();
    if (spots.isEmpty) return const Center(child: Text('暂无数据'));

    final weights = data.map((d) => d['weight'] as double? ?? 0.0).toList();
    final minWeight = weights.reduce((a, b) => a < b ? a : b);
    final maxWeight = weights.reduce((a, b) => a > b ? a : b);
    final weightRange = maxWeight - minWeight;
    final adjustedMinWeight = minWeight - (weightRange * 0.1);
    final adjustedMaxWeight = maxWeight + (weightRange * 0.1);

    return LineChart(
      LineChartData(
        minX: 0,
        maxX: (spots.length - 1).toDouble(),
        minY: adjustedMinWeight,
        maxY: adjustedMaxWeight,
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: (adjustedMaxWeight - adjustedMinWeight) / 5,
          getDrawingHorizontalLine: (value) => FlLine(color: Colors.grey[300]!, strokeWidth: 1),
        ),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 50,
              getTitlesWidget:
                  (value, meta) =>
                      Text('${value.toStringAsFixed(1)}kg', style: const TextStyle(fontSize: 12, color: Colors.grey)),
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: (spots.length / 5).ceil().toDouble(),
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < data.length) {
                  final recordDate = data[index]['record_date'];
                  if (recordDate != null) {
                    final date = DateTime.parse(recordDate.toString());
                    return Text('${date.month}/${date.day}', style: const TextStyle(fontSize: 12, color: Colors.grey));
                  }
                }
                return const Text('');
              },
            ),
          ),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: true, border: Border.all(color: Colors.grey[300]!, width: 1)),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: color,
            barWidth: 3,
            dotData: FlDotData(
              show: true,
              getDotPainter:
                  (spot, percent, barData, index) =>
                      FlDotCirclePainter(radius: 4, color: color, strokeWidth: 2, strokeColor: Colors.white),
            ),
            belowBarData: BarAreaData(show: true, color: color.withValues(alpha: 0.1)),
          ),
        ],
        lineTouchData: LineTouchData(
          enabled: true,
          touchTooltipData: LineTouchTooltipData(
            getTooltipItems: (touchedSpots) {
              return touchedSpots.map((spot) {
                final index = spot.x.toInt();
                if (index >= 0 && index < data.length) {
                  final recordDate = data[index]['record_date'];
                  if (recordDate != null) {
                    final date = DateTime.parse(recordDate.toString());
                    final weight = spot.y;
                    return LineTooltipItem(
                      '${date.month}/${date.day}\n${weight.toStringAsFixed(1)}kg',
                      TextStyle(color: color, fontWeight: FontWeight.bold),
                    );
                  }
                }
                return null;
              }).toList();
            },
          ),
        ),
      ),
    );
  }

  /// 生成图表数据点
  List<FlSpot> _generateSpots() {
    return data.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final weight = item['weight'] as double? ?? 0.0;
      return FlSpot(index.toDouble(), weight);
    }).toList();
  }
}
