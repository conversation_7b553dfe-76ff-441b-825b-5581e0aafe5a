import 'package:get/get.dart';
import 'package:health/health.dart';
import '../../../shared/controllers/base_controller.dart';
import '../../../shared/models/health_data_type.dart';
import '../../index/controllers/index_controller.dart';
import '../../../data/services/sqflite/dao/impl/body_data_dao.dart';
import '../../../../core/utils/logger_util.dart';

/// 健康数据详情页面控制器
class HealthDetailController extends BaseController {
  /// 健康数据类型
  late final HealthDataTypeEnum dataType;

  /// 健康数据配置
  late final HealthDataTypeConfig config;

  /// 详细健康数据
  final RxList<HealthDataPoint> _detailedData = <HealthDataPoint>[].obs;

  /// 体重趋势数据（用于体重变化类型）
  final RxList<Map<String, dynamic>> _weightTrendData = <Map<String, dynamic>>[].obs;

  /// 总数值
  final RxString _totalValue = '0'.obs;

  /// 是否有数据
  final RxBool _hasData = false.obs;

  // Getters
  List<HealthDataPoint> get detailedData => _detailedData;
  String get totalValue => _totalValue.value;
  bool get hasData => _hasData.value;

  @override
  void onInit() {
    super.onInit();

    // 从路由参数获取数据类型
    final typeString = Get.parameters['type'];
    if (typeString != null) {
      dataType = HealthDataTypeEnum.values.firstWhere(
        (e) => e.toString().split('.').last == typeString,
        orElse: () => HealthDataTypeEnum.steps,
      );
    } else {
      dataType = HealthDataTypeEnum.steps;
    }

    // 获取配置
    config = HealthDataTypeConfigs.getConfig(dataType);

    // 加载数据
    _loadData();
  }

  /// 加载数据
  Future<void> _loadData() async {
    try {
      final indexController = Get.find<IndexController>();

      List<HealthDataPoint> data = [];
      String total = '0';

      switch (dataType) {
        case HealthDataTypeEnum.steps:
          data = indexController.detailedStepsData;
          total = '${indexController.todaySteps}';
          break;
        case HealthDataTypeEnum.distance:
          data = indexController.detailedDistanceData;
          total = indexController.todayDistance.toStringAsFixed(1);
          break;
        case HealthDataTypeEnum.flights:
          data = indexController.detailedFlightsData;
          total = '${indexController.todayFlightsClimbed}';
          break;
        case HealthDataTypeEnum.activeCalories:
          data = indexController.detailedActiveCaloriesData;
          total = '${indexController.todayActiveCalories.round()}';
          break;
        case HealthDataTypeEnum.basalCalories:
          data = indexController.detailedBasalCaloriesData;
          total = '${indexController.todayBasalCalories.round()}';
          break;
        case HealthDataTypeEnum.totalCalories:
          // 总消耗使用活动消耗和基础代谢的组合数据
          data = [...indexController.detailedActiveCaloriesData, ...indexController.detailedBasalCaloriesData];
          total = '${indexController.todayTotalBurnedCalories.round()}';
          break;
        case HealthDataTypeEnum.weightChange:
          // 体重变化数据从数据库获取
          await _loadWeightData();
          return; // 体重数据处理完成后直接返回
      }

      // 按时间排序
      data.sort((a, b) => a.dateFrom.compareTo(b.dateFrom));

      _detailedData.value = data;
      _totalValue.value = total;
      _hasData.value = data.isNotEmpty;
    } catch (e) {
      LoggerUtil.e('加载健康数据失败: $e');
      _hasData.value = false;
    }
  }

  /// 加载体重数据
  Future<void> _loadWeightData() async {
    try {
      final bodyDataDao = BodyDataDao();

      // 获取最近两次体重记录
      final recentRecords = await bodyDataDao.getRecentTwoBodyData();

      if (recentRecords.length >= 2) {
        final latestRecord = recentRecords[0];
        final previousRecord = recentRecords[1];

        final latestWeight = latestRecord.weight ?? 0.0;
        final previousWeight = previousRecord.weight ?? 0.0;
        final weightChange = latestWeight - previousWeight;

        _totalValue.value = weightChange.toStringAsFixed(1);

        // 获取体重趋势数据（最近30天）
        final trendData = await bodyDataDao.getWeightTrend(30);
        _weightTrendData.value = trendData;

        _hasData.value = trendData.isNotEmpty;
      } else {
        _hasData.value = false;
      }
    } catch (e) {
      LoggerUtil.e('加载体重数据失败: $e');
      _hasData.value = false;
    }
  }

  /// 刷新数据
  Future<void> refreshData() async {
    await executeAsync(() async {
      if (dataType == HealthDataTypeEnum.weightChange) {
        await _loadWeightData();
      } else {
        // 先刷新IndexController的数据
        final indexController = Get.find<IndexController>();
        await indexController.getTodayHealthData();

        // 重新加载本页面数据
        await _loadData();
      }
    });
  }

  /// 格式化数值显示
  String getFormattedValue() {
    switch (dataType) {
      case HealthDataTypeEnum.distance:
        return '$totalValue ${config.unit}';
      case HealthDataTypeEnum.weightChange:
        final value = double.tryParse(totalValue) ?? 0.0;
        final sign = value >= 0 ? '+' : '';
        return '$sign$totalValue ${config.unit}';
      case HealthDataTypeEnum.steps:
      case HealthDataTypeEnum.flights:
      case HealthDataTypeEnum.activeCalories:
      case HealthDataTypeEnum.basalCalories:
      case HealthDataTypeEnum.totalCalories:
        return '$totalValue ${config.unit}';
    }
  }

  /// 获取图表数据
  List<HealthDataPoint> getChartData() {
    return _detailedData;
  }

  /// 获取列表数据
  List<HealthDataPoint> getListData() {
    return _detailedData;
  }

  /// 获取体重趋势数据
  List<Map<String, dynamic>> getWeightTrendData() {
    return _weightTrendData;
  }
}
