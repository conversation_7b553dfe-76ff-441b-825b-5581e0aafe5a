import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_ruler_picker/flutter_ruler_picker.dart';

import '../../../shared/controllers/base_controller.dart';
import '../../../data/services/hive/hive_service.dart';
import '../../../data/services/sqflite/dao/impl/body_data_dao.dart';
import '../../../data/services/sqflite/dao/impl/health_goal_dao.dart';
import '../../../data/models/health_goal.dart';
import '../../../../core/constants/health_goals.dart';
import '../../../../core/utils/logger_util.dart';

/// 目标设定控制器
/// 负责管理用户的健康目标设定，包括目标体重、卡路里摄入、活动水平等
class GoalSettingController extends BaseController {
  /// 数据访问对象
  final BodyDataDao _bodyDataDao = BodyDataDao();
  final HealthGoalDao _healthGoalDao = HealthGoalDao();

  /// 表单相关
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  /// 刻度尺控制器
  late RulerPickerController targetWeightRulerController;

  /// 目标类型（多选，存储逗号分隔的目标类型）
  final RxString _selectedGoalTypes = ''.obs;
  final List<String> goalTypes = HealthGoals.options;

  /// 目标数据
  final RxDouble _targetWeight = 60.0.obs; // 设置默认值60kg
  final RxInt _targetDays = 90.obs;

  /// 保存状态
  final RxBool _isSaving = false.obs;

  /// 当前体重（从用户数据获取）
  final RxDouble currentWeight = 0.0.obs;
  final RxDouble currentHeight = 0.0.obs;
  final RxDouble currentBMI = 0.0.obs;

  /// 目标期限选项（与引导页保持一致）
  final List<Map<String, dynamic>> targetDaysOptions = [
    {'label': '1个月', 'days': 30},
    {'label': '3个月', 'days': 90},
    {'label': '6个月', 'days': 180},
    {'label': '1年', 'days': 365},
  ];

  // Getter方法，提供对私有变量的只读访问
  String get selectedGoalTypes => _selectedGoalTypes.value;
  double get targetWeight => _targetWeight.value;
  int get targetDays => _targetDays.value;
  bool get isSaving => _isSaving.value;

  @override
  void onInit() {
    super.onInit();
    // 初始化刻度尺控制器
    targetWeightRulerController = RulerPickerController(value: _targetWeight.value);
    _cleanupOldGoalTypeData(); // 清理旧数据
    _loadCurrentData();
    _loadGoalData();
  }

  /// 刷新目标类型
  Future<void> refreshGoalType() async {
    await _loadGoalTypes();
  }

  /// 清理旧的goalType数据（迁移用）
  Future<void> _cleanupOldGoalTypeData() async {
    try {
      // 检查是否存在旧的goalType数据
      final oldGoalType = await HiveService.getData("goals", "goalType");
      if (oldGoalType != null) {
        // 如果用户的健康目标为空，将旧的goalType迁移过去
        final currentTargetDescription = await HiveService.getData("user", "targetDescription");
        if (currentTargetDescription == null || currentTargetDescription.isEmpty) {
          await HiveService.saveData("user", "targetDescription", oldGoalType);
        }
        // 删除旧的goalType数据
        await HiveService.deleteData("goals", "goalType");
      }
    } catch (e) {
      // 忽略清理错误，不影响主要功能
    }
  }

  @override
  void onClose() {
    // RulerPickerController 不需要手动dispose
    super.onClose();
  }

  /// 加载当前身体数据
  Future<void> _loadCurrentData() async {
    try {
      // 从SQLite数据库加载最新的身体数据
      final latestBodyData = await _bodyDataDao.getLatestBodyData();

      if (latestBodyData != null) {
        // 设置当前体重和身高
        if (latestBodyData.weight != null) {
          currentWeight.value = latestBodyData.weight!; // 直接使用double值
        }
        if (latestBodyData.height != null) {
          currentHeight.value = latestBodyData.height!.toDouble();
        }

        // 使用数据库中的BMI，如果没有则计算
        if (latestBodyData.bmi != null) {
          currentBMI.value = latestBodyData.bmi!;
        } else if (currentWeight.value > 0 && currentHeight.value > 0) {
          final heightInMeters = currentHeight.value / 100;
          currentBMI.value = currentWeight.value / (heightInMeters * heightInMeters);
        }
      } else {
        // 如果数据库中没有数据，尝试从Hive读取（向后兼容）
        final weightData = await HiveService.getData("bodyData", "weight");
        final heightData = await HiveService.getData("bodyData", "height");

        if (weightData != null) {
          currentWeight.value = double.tryParse(weightData.toString()) ?? 0.0;
        }
        if (heightData != null) {
          currentHeight.value = double.tryParse(heightData.toString()) ?? 0.0;
        }

        // 计算BMI
        if (currentWeight.value > 0 && currentHeight.value > 0) {
          final heightInMeters = currentHeight.value / 100;
          currentBMI.value = currentWeight.value / (heightInMeters * heightInMeters);
        }
      }
    } catch (e) {
      // 静默处理加载错误
    }
  }

  /// 加载目标数据
  Future<void> _loadGoalData() async {
    try {
      setLoading(true);

      // 优先从数据库加载最新的健康目标数据
      final latestHealthGoal = await _healthGoalDao.getLatestHealthGoal();
      LoggerUtil.d('数据库查询结果: $latestHealthGoal');

      if (latestHealthGoal != null) {
        // 从数据库加载数据
        LoggerUtil.i('从数据库加载健康目标数据');
        if (latestHealthGoal.goalTypes != null) {
          _selectedGoalTypes.value = latestHealthGoal.goalTypes!;
          LoggerUtil.d('加载目标类型: ${latestHealthGoal.goalTypes}');
        }
        if (latestHealthGoal.targetWeight != null) {
          _targetWeight.value = latestHealthGoal.targetWeight!;
          targetWeightRulerController.value = _targetWeight.value;
          LoggerUtil.d('加载目标体重: ${latestHealthGoal.targetWeight}kg');
        }
        if (latestHealthGoal.targetDays != null) {
          _targetDays.value = latestHealthGoal.targetDays!;
          LoggerUtil.d('加载目标期限: ${latestHealthGoal.targetDays}天');
        }
      } else {
        // 如果数据库中没有数据，从Hive加载（向后兼容）
        await _loadGoalTypes();

        final savedTargetWeight = await HiveService.getData("goals", "targetWeight");
        final savedTargetDays = await HiveService.getData("goals", "targetDays");

        if (savedTargetWeight != null) {
          _targetWeight.value = double.tryParse(savedTargetWeight.toString()) ?? 60.0;
          targetWeightRulerController.value = _targetWeight.value;
        }
        if (savedTargetDays != null) {
          _targetDays.value = int.tryParse(savedTargetDays.toString()) ?? 90;
        }
      }
    } catch (e) {
      // ignore: avoid_print
      print('加载目标数据失败: $e');
    } finally {
      setLoading(false);
    }
  }

  /// 加载目标类型（从goals存储中读取）
  Future<void> _loadGoalTypes() async {
    try {
      final savedGoalTypes = await HiveService.getData("goals", "goalTypes");
      if (savedGoalTypes != null && savedGoalTypes.isNotEmpty) {
        _selectedGoalTypes.value = savedGoalTypes;
      } else {
        // 如果没有保存的目标类型，不设置默认值，保持空
        _selectedGoalTypes.value = '';
      }
    } catch (e) {
      // ignore: avoid_print
      print('加载目标类型失败: $e');
    }
  }

  /// 切换目标类型选择状态（多选）
  void toggleGoalType(String type) {
    List<String> currentGoals = selectedGoalTypes.isNotEmpty ? selectedGoalTypes.split(',') : [];

    if (currentGoals.contains(type)) {
      currentGoals.remove(type);
    } else {
      currentGoals.add(type);
    }

    _selectedGoalTypes.value = currentGoals.join(',');
  }

  /// 获取当前选中的目标类型列表
  List<String> getSelectedGoalTypes() {
    return selectedGoalTypes.isNotEmpty ? selectedGoalTypes.split(',') : [];
  }

  /// 检查目标类型是否被选中
  bool isGoalTypeSelected(String type) {
    return getSelectedGoalTypes().contains(type);
  }

  /// 更新目标体重（从刻度尺回调）
  void updateTargetWeight(double value) {
    if (value >= 30 && value <= 150) {
      _targetWeight.value = double.parse(value.toStringAsFixed(1));
    }
  }

  /// 更新目标期限
  void updateTargetDays(int days) {
    _targetDays.value = days;
  }

  /// 根据天数获取对应的标签
  String getTargetDaysLabel() {
    for (final option in targetDaysOptions) {
      if (option['days'] == targetDays) {
        return option['label'];
      }
    }
    return '$targetDays天'; // 默认显示天数
  }

  /// 计算推荐卡路里（保留方法以避免其他地方调用出错，但不再执行实际逻辑）
  void calculateRecommendedCalories() {
    // 由于已移除每日卡路里目标功能，此方法保留为空实现
    // 避免其他地方调用此方法时出错
  }

  /// 保存目标设定
  Future<void> saveGoalSettings() async {
    // 验证表单
    if (!formKey.currentState!.validate()) {
      Get.snackbar(
        '验证失败',
        '请检查输入的信息是否完整和正确',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.orange.shade100,
        colorText: Colors.orange.shade800,
        duration: const Duration(seconds: 2),
      );
      return;
    }

    try {
      _isSaving.value = true;

      // 保存到数据库
      await _saveHealthGoalToDatabase();

      // 同时保存到Hive（向后兼容）
      await HiveService.saveData("goals", "goalTypes", selectedGoalTypes);
      await HiveService.saveData("goals", "targetWeight", targetWeight);
      await HiveService.saveData("goals", "targetDays", targetDays);
      await HiveService.saveData("goals", "createdTime", DateTime.now().toIso8601String());

      // 先返回上一页
      Get.back();

      // 然后显示成功提示
      Get.snackbar(
        '保存成功',
        '目标设定已保存',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        '保存失败',
        '保存目标设定失败: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
        duration: const Duration(seconds: 3),
      );
    } finally {
      _isSaving.value = false;
    }
  }

  /// 重置目标设定
  void resetGoalSettings() {
    _selectedGoalTypes.value = '';
    _targetWeight.value = 60.0;
    _targetDays.value = 90;
    targetWeightRulerController.value = _targetWeight.value;
  }

  /// 获取目标描述
  String getGoalDescription() {
    if (currentWeight.value <= 0 || targetWeight <= 0) {
      return '请设置目标体重';
    }

    final weightDiff = targetWeight - currentWeight.value;
    final weightDiffAbs = weightDiff.abs();
    final direction = weightDiff > 0 ? '增加' : '减少';
    final durationLabel = getTargetDaysLabel();

    return '目标$direction ${weightDiffAbs.toStringAsFixed(1)}kg，预计$durationLabel完成';
  }

  /// 获取每周建议减重/增重
  String getWeeklyWeightChange() {
    if (currentWeight.value <= 0 || targetWeight <= 0 || targetDays <= 0) {
      return '0.0kg/周';
    }

    final weightDiff = (targetWeight - currentWeight.value).abs();
    final weeks = targetDays / 7;
    final weeklyChange = weightDiff / weeks;

    return '${weeklyChange.toStringAsFixed(1)}kg/周';
  }

  /// 保存健康目标到数据库
  Future<void> _saveHealthGoalToDatabase() async {
    try {
      LoggerUtil.i('开始保存健康目标到数据库');
      LoggerUtil.d('目标类型: $selectedGoalTypes');
      LoggerUtil.d('目标体重: ${targetWeight}kg');
      LoggerUtil.d('目标期限: $targetDays天');

      // 查询最新的健康目标记录作为基础
      final latestHealthGoal = await _healthGoalDao.getLatestHealthGoal();

      if (latestHealthGoal != null) {
        // 更新现有记录
        LoggerUtil.i('更新现有健康目标记录');
        final updatedHealthGoal = latestHealthGoal.copyWith(
          goalTypes: selectedGoalTypes.isNotEmpty ? selectedGoalTypes : null,
          targetWeight: targetWeight,
          targetDays: targetDays,
        );
        await _healthGoalDao.update(updatedHealthGoal);
        LoggerUtil.i('健康目标记录更新成功');
      } else {
        // 创建新的健康目标记录
        LoggerUtil.i('创建新的健康目标记录');
        final newHealthGoal = HealthGoal(
          recordDate: DateTime.now(),
          goalTypes: selectedGoalTypes.isNotEmpty ? selectedGoalTypes : null,
          targetWeight: targetWeight,
          targetDays: targetDays,
        );
        await _healthGoalDao.insert(newHealthGoal);
        LoggerUtil.i('健康目标记录创建成功');
      }
    } catch (e) {
      LoggerUtil.e('保存健康目标到数据库失败: $e');
      rethrow;
    }
  }
}
