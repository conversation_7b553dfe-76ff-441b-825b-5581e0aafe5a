import 'package:flutter/foundation.dart';
import 'logger_util.dart';
import 'performance_monitor.dart';
import 'code_optimizer.dart';
import 'database_optimizer.dart';
import 'memory_optimizer.dart';

/// 综合优化管理器
/// 统一管理所有优化工具和监控
class OptimizationManager {
  static bool _isInitialized = false;
  static bool _isMonitoring = false;

  /// 初始化优化管理器
  static void initialize() {
    if (_isInitialized) return;
    
    if (kDebugMode) {
      LoggerUtil.i('🚀 初始化优化管理器...');
      
      // 启动性能监控
      PerformanceMonitor.startMonitoring();
      
      // 启动内存监控
      MemoryOptimizer.startMemoryMonitoring();
      
      _isInitialized = true;
      _isMonitoring = true;
      
      LoggerUtil.i('✅ 优化管理器初始化完成');
    }
  }

  /// 生成综合优化报告
  static void generateComprehensiveReport() {
    if (kDebugMode) {
      LoggerUtil.i('📊 ========== 综合优化报告 ==========');
      
      // 性能报告
      PerformanceMonitor.printPerformanceReport();
      
      // 代码优化报告
      CodeOptimizer.generateOptimizationReport();
      
      // 数据库查询报告
      DatabaseOptimizer.generateQueryReport();
      
      // 内存使用报告
      MemoryOptimizer.generateMemoryReport();
      
      // 综合建议
      _generateComprehensiveSuggestions();
      
      LoggerUtil.i('📊 ================================');
    }
  }

  /// 生成综合优化建议
  static void _generateComprehensiveSuggestions() {
    final allSuggestions = <String>[];
    
    // 收集所有优化建议
    allSuggestions.addAll(CodeOptimizer.getOptimizationSuggestions());
    allSuggestions.addAll(DatabaseOptimizer.getOptimizationSuggestions());
    allSuggestions.addAll(MemoryOptimizer.getMemoryOptimizationSuggestions());
    
    if (allSuggestions.isNotEmpty) {
      LoggerUtil.i('🎯 综合优化建议:');
      for (int i = 0; i < allSuggestions.length; i++) {
        LoggerUtil.i('  ${i + 1}. ${allSuggestions[i]}');
      }
      
      // 优先级建议
      _prioritizeSuggestions(allSuggestions);
    } else {
      LoggerUtil.i('✅ 暂无优化建议，系统运行良好');
    }
  }

  /// 优化建议优先级排序
  static void _prioritizeSuggestions(List<String> suggestions) {
    final highPriority = suggestions.where((s) => 
      s.contains('内存泄漏') || s.contains('执行时间过长') || s.contains('频繁调用')
    ).toList();
    
    final mediumPriority = suggestions.where((s) => 
      s.contains('缓存') || s.contains('重复创建')
    ).toList();
    
    if (highPriority.isNotEmpty) {
      LoggerUtil.w('🔴 高优先级问题:');
      for (final suggestion in highPriority) {
        LoggerUtil.w('  • $suggestion');
      }
    }
    
    if (mediumPriority.isNotEmpty) {
      LoggerUtil.i('🟡 中优先级建议:');
      for (final suggestion in mediumPriority) {
        LoggerUtil.i('  • $suggestion');
      }
    }
  }

  /// 执行自动优化
  static void performAutoOptimization() {
    if (kDebugMode) {
      LoggerUtil.i('🔧 执行自动优化...');
      
      // 清理过期缓存
      DatabaseOptimizer.cleanExpiredCache();
      
      // 强制垃圾回收建议
      MemoryOptimizer.forceGarbageCollection();
      
      // 检查长时间运行的操作
      PerformanceMonitor.checkLongRunningOperations();
      
      LoggerUtil.i('✅ 自动优化完成');
    }
  }

  /// 定期优化任务
  static void startPeriodicOptimization() {
    if (kDebugMode && !_isMonitoring) {
      _isMonitoring = true;
      
      // 每10分钟执行一次自动优化
      Future.doWhile(() async {
        await Future.delayed(const Duration(minutes: 10));
        performAutoOptimization();
        return _isMonitoring;
      });
      
      // 每30分钟生成一次报告
      Future.doWhile(() async {
        await Future.delayed(const Duration(minutes: 30));
        generateComprehensiveReport();
        return _isMonitoring;
      });
      
      LoggerUtil.i('⏰ 定期优化任务已启动');
    }
  }

  /// 停止监控
  static void stopMonitoring() {
    _isMonitoring = false;
    LoggerUtil.i('⏹️ 优化监控已停止');
  }

  /// 重置所有统计数据
  static void resetAllStatistics() {
    if (kDebugMode) {
      PerformanceMonitor.clearStatistics();
      CodeOptimizer.clearStatistics();
      DatabaseOptimizer.clearAll();
      MemoryOptimizer.clearAll();
      
      LoggerUtil.i('🔄 所有统计数据已重置');
    }
  }

  /// 获取系统健康状态
  static Map<String, dynamic> getSystemHealthStatus() {
    if (!kDebugMode) return {};
    
    final status = <String, dynamic>{};
    
    // 性能状态
    final performanceMetrics = PerformanceMonitor.getAllMetrics();
    status['performance'] = {
      'totalOperations': performanceMetrics.length,
      'averageTime': performanceMetrics.isNotEmpty 
        ? performanceMetrics.values.map((d) => d.inMilliseconds).reduce((a, b) => a + b) / performanceMetrics.length
        : 0,
    };
    
    // 数据库状态
    status['database'] = {
      'cacheHitRate': DatabaseOptimizer.getCacheHitRate(),
    };
    
    // 内存状态
    final memoryWarnings = MemoryOptimizer.getMemoryOptimizationSuggestions();
    status['memory'] = {
      'warningCount': memoryWarnings.length,
      'hasMemoryLeaks': memoryWarnings.any((w) => w.contains('内存泄漏')),
    };
    
    return status;
  }

  /// 导出优化数据
  static Map<String, dynamic> exportOptimizationData() {
    if (!kDebugMode) return {};
    
    return {
      'timestamp': DateTime.now().toIso8601String(),
      'performance': PerformanceMonitor.getAllMetrics().map(
        (key, value) => MapEntry(key, value.inMilliseconds)
      ),
      'suggestions': {
        'code': CodeOptimizer.getOptimizationSuggestions(),
        'database': DatabaseOptimizer.getOptimizationSuggestions(),
        'memory': MemoryOptimizer.getMemoryOptimizationSuggestions(),
      },
      'systemHealth': getSystemHealthStatus(),
    };
  }

  /// 应用启动优化检查
  static void performStartupOptimizationCheck() {
    if (kDebugMode) {
      LoggerUtil.i('🚀 执行启动优化检查...');
      
      // 延迟执行，避免影响启动性能
      Future.delayed(const Duration(seconds: 5), () {
        generateComprehensiveReport();
        
        // 如果发现严重问题，立即提醒
        final systemHealth = getSystemHealthStatus();
        if (systemHealth['memory']?['hasMemoryLeaks'] == true) {
          LoggerUtil.w('🚨 检测到潜在内存泄漏，请检查优化报告');
        }
      });
    }
  }
}
