import 'package:flutter/foundation.dart';
import 'logger_util.dart';

/// 数据库查询优化器
/// 提供数据库查询缓存和优化建议
class DatabaseOptimizer {
  static final Map<String, dynamic> _queryCache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static final Map<String, int> _queryStats = {};
  static const Duration _defaultCacheExpiry = Duration(minutes: 5);

  /// 缓存查询结果
  static void cacheQuery(String queryKey, dynamic result, {Duration? expiry}) {
    if (kDebugMode) {
      _queryCache[queryKey] = result;
      _cacheTimestamps[queryKey] = DateTime.now();
      LoggerUtil.d('缓存查询结果: $queryKey');
    }
  }

  /// 获取缓存的查询结果
  static T? getCachedQuery<T>(String queryKey, {Duration? expiry}) {
    if (kDebugMode) {
      final timestamp = _cacheTimestamps[queryKey];
      if (timestamp != null) {
        final age = DateTime.now().difference(timestamp);
        final maxAge = expiry ?? _defaultCacheExpiry;
        
        if (age <= maxAge) {
          LoggerUtil.d('使用缓存查询结果: $queryKey');
          return _queryCache[queryKey] as T?;
        } else {
          // 缓存过期，清理
          _queryCache.remove(queryKey);
          _cacheTimestamps.remove(queryKey);
          LoggerUtil.d('缓存已过期，清理: $queryKey');
        }
      }
    }
    return null;
  }

  /// 记录查询统计
  static void recordQuery(String queryType) {
    if (kDebugMode) {
      _queryStats[queryType] = (_queryStats[queryType] ?? 0) + 1;
    }
  }

  /// 清理过期缓存
  static void cleanExpiredCache() {
    if (kDebugMode) {
      final now = DateTime.now();
      final expiredKeys = <String>[];

      for (final entry in _cacheTimestamps.entries) {
        final age = now.difference(entry.value);
        if (age > _defaultCacheExpiry) {
          expiredKeys.add(entry.key);
        }
      }

      for (final key in expiredKeys) {
        _queryCache.remove(key);
        _cacheTimestamps.remove(key);
      }

      if (expiredKeys.isNotEmpty) {
        LoggerUtil.d('清理过期缓存: ${expiredKeys.length} 个');
      }
    }
  }

  /// 生成查询统计报告
  static void generateQueryReport() {
    if (kDebugMode) {
      LoggerUtil.i('🗄️ ========== 数据库查询报告 ==========');
      
      if (_queryStats.isEmpty) {
        LoggerUtil.i('📊 暂无查询统计数据');
      } else {
        LoggerUtil.i('📊 查询统计:');
        final sortedStats = _queryStats.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));
        
        for (final entry in sortedStats) {
          LoggerUtil.i('  ${entry.key}: ${entry.value} 次');
          
          // 提供优化建议
          if (entry.value > 10) {
            LoggerUtil.w('⚠️ ${entry.key} 查询频繁，建议添加缓存或优化查询逻辑');
          }
        }
      }

      LoggerUtil.i('💾 缓存统计: ${_queryCache.length} 个缓存项');
      LoggerUtil.i('🗄️ ================================');
    }
  }

  /// 清理所有缓存和统计
  static void clearAll() {
    _queryCache.clear();
    _cacheTimestamps.clear();
    _queryStats.clear();
    LoggerUtil.d('清理所有数据库优化器数据');
  }

  /// 获取缓存命中率
  static double getCacheHitRate() {
    if (_queryStats.isEmpty) return 0.0;
    
    final totalQueries = _queryStats.values.fold(0, (sum, count) => sum + count);
    final cacheHits = _queryCache.length;
    
    return totalQueries > 0 ? (cacheHits / totalQueries) * 100 : 0.0;
  }

  /// 优化建议
  static List<String> getOptimizationSuggestions() {
    final suggestions = <String>[];
    
    if (kDebugMode) {
      // 检查频繁查询
      for (final entry in _queryStats.entries) {
        if (entry.value > 10) {
          suggestions.add('🔄 ${entry.key} 查询过于频繁 (${entry.value}次)，建议添加缓存');
        }
      }

      // 检查缓存命中率
      final hitRate = getCacheHitRate();
      if (hitRate < 50 && _queryStats.isNotEmpty) {
        suggestions.add('📉 缓存命中率较低 (${hitRate.toStringAsFixed(1)}%)，建议优化缓存策略');
      }

      // 检查缓存大小
      if (_queryCache.length > 100) {
        suggestions.add('💾 缓存项过多 (${_queryCache.length}个)，建议定期清理或减少缓存时间');
      }
    }

    return suggestions;
  }

  /// 数据库查询包装器（带缓存）
  static Future<T> cachedQuery<T>(
    String queryKey,
    Future<T> Function() queryFunction, {
    Duration? cacheExpiry,
  }) async {
    // 尝试从缓存获取
    final cached = getCachedQuery<T>(queryKey, expiry: cacheExpiry);
    if (cached != null) {
      return cached;
    }

    // 执行查询
    recordQuery(queryKey);
    final result = await queryFunction();
    
    // 缓存结果
    cacheQuery(queryKey, result, expiry: cacheExpiry);
    
    return result;
  }

  /// 批量查询优化器
  static Future<List<T>> batchQuery<T>(
    List<String> queryKeys,
    Future<T> Function(String key) queryFunction, {
    Duration? cacheExpiry,
  }) async {
    final results = <T>[];
    final uncachedKeys = <String>[];

    // 先检查缓存
    for (final key in queryKeys) {
      final cached = getCachedQuery<T>(key, expiry: cacheExpiry);
      if (cached != null) {
        results.add(cached);
      } else {
        uncachedKeys.add(key);
      }
    }

    // 批量执行未缓存的查询
    for (final key in uncachedKeys) {
      recordQuery(key);
      final result = await queryFunction(key);
      cacheQuery(key, result, expiry: cacheExpiry);
      results.add(result);
    }

    LoggerUtil.d('批量查询完成: 缓存命中 ${queryKeys.length - uncachedKeys.length}/${queryKeys.length}');
    
    return results;
  }

  /// 预热缓存
  static Future<void> warmupCache(Map<String, Future<dynamic> Function()> queries) async {
    LoggerUtil.i('开始预热数据库缓存...');
    
    for (final entry in queries.entries) {
      try {
        final result = await entry.value();
        cacheQuery(entry.key, result);
      } catch (e) {
        LoggerUtil.e('预热缓存失败: ${entry.key}, 错误: $e');
      }
    }
    
    LoggerUtil.i('缓存预热完成: ${queries.length} 个查询');
  }
}
