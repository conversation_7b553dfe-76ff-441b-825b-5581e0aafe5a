import 'package:flutter/foundation.dart';
import 'logger_util.dart';

/// 内存优化工具
/// 提供内存使用监控和优化建议
class MemoryOptimizer {
  static final Map<String, int> _controllerInstances = {};
  static final Map<String, DateTime> _controllerCreationTimes = {};
  static final List<String> _memoryWarnings = [];

  /// 记录控制器创建
  static void recordControllerCreation(String controllerName) {
    if (kDebugMode) {
      _controllerInstances[controllerName] = (_controllerInstances[controllerName] ?? 0) + 1;
      _controllerCreationTimes[controllerName] = DateTime.now();

      LoggerUtil.d('控制器创建: $controllerName (实例数: ${_controllerInstances[controllerName]})');

      // 检查是否有重复创建
      if (_controllerInstances[controllerName]! > 1) {
        _memoryWarnings.add('⚠️ 控制器 $controllerName 被重复创建 ${_controllerInstances[controllerName]} 次');
      }
    }
  }

  /// 记录控制器销毁
  static void recordControllerDestroy(String controllerName) {
    if (kDebugMode) {
      final currentCount = _controllerInstances[controllerName] ?? 0;
      if (currentCount > 0) {
        _controllerInstances[controllerName] = currentCount - 1;
        LoggerUtil.d('控制器销毁: $controllerName (剩余实例数: ${_controllerInstances[controllerName]})');
      }
    }
  }

  /// 检查长期存在的控制器
  static void checkLongLivedControllers() {
    if (kDebugMode) {
      final now = DateTime.now();

      for (final entry in _controllerCreationTimes.entries) {
        final age = now.difference(entry.value);
        if (age.inMinutes > 30) {
          // 超过30分钟
          _memoryWarnings.add('🕐 控制器 ${entry.key} 存在时间过长: ${age.inMinutes} 分钟，可能存在内存泄漏');
        }
      }
    }
  }

  /// 检查GetX控制器状态
  static void checkGetXControllers() {
    if (kDebugMode) {
      final registeredControllers = <String>[];

      // 这里可以添加具体的GetX控制器检查逻辑
      // 由于GetX内部API的限制，这里提供一个基础框架

      LoggerUtil.d('GetX控制器检查完成，注册的控制器数量: ${registeredControllers.length}');
    }
  }

  /// 强制垃圾回收（仅调试模式）
  static void forceGarbageCollection() {
    if (kDebugMode) {
      // 在Flutter中，我们无法直接触发GC，但可以提供建议
      LoggerUtil.i('💾 建议进行内存清理');

      // 清理过期的控制器记录
      _cleanupExpiredRecords();
    }
  }

  /// 清理过期记录
  static void _cleanupExpiredRecords() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _controllerCreationTimes.entries) {
      final age = now.difference(entry.value);
      if (age.inHours > 24) {
        // 超过24小时的记录
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _controllerCreationTimes.remove(key);
      if (_controllerInstances[key] == 0) {
        _controllerInstances.remove(key);
      }
    }

    if (expiredKeys.isNotEmpty) {
      LoggerUtil.d('清理过期控制器记录: ${expiredKeys.length} 个');
    }
  }

  /// 生成内存使用报告
  static void generateMemoryReport() {
    if (kDebugMode) {
      LoggerUtil.i('💾 ========== 内存使用报告 ==========');

      // 控制器实例统计
      if (_controllerInstances.isNotEmpty) {
        LoggerUtil.i('🎮 控制器实例统计:');
        final sortedControllers = _controllerInstances.entries.toList()..sort((a, b) => b.value.compareTo(a.value));

        for (final entry in sortedControllers) {
          if (entry.value > 0) {
            LoggerUtil.i('  ${entry.key}: ${entry.value} 个实例');
          }
        }
      }

      // 内存警告
      if (_memoryWarnings.isNotEmpty) {
        LoggerUtil.w('⚠️ 内存警告:');
        for (final warning in _memoryWarnings) {
          LoggerUtil.w('  $warning');
        }
      } else {
        LoggerUtil.i('✅ 暂无内存警告');
      }

      LoggerUtil.i('💾 ================================');
    }
  }

  /// 获取内存优化建议
  static List<String> getMemoryOptimizationSuggestions() {
    final suggestions = <String>[];

    if (kDebugMode) {
      checkLongLivedControllers();

      // 检查重复实例
      for (final entry in _controllerInstances.entries) {
        if (entry.value > 1) {
          suggestions.add('🔄 控制器 ${entry.key} 有 ${entry.value} 个实例，建议使用单例模式');
        }
      }

      // 添加现有警告
      suggestions.addAll(_memoryWarnings);
    }

    return suggestions;
  }

  /// 清理所有统计数据
  static void clearAll() {
    _controllerInstances.clear();
    _controllerCreationTimes.clear();
    _memoryWarnings.clear();
    LoggerUtil.d('清理所有内存优化器数据');
  }

  /// 监控大对象创建
  static void monitorLargeObjectCreation(String objectType, int sizeInBytes) {
    if (kDebugMode) {
      if (sizeInBytes > 1024 * 1024) {
        // 大于1MB
        _memoryWarnings.add('📦 创建大对象: $objectType (${(sizeInBytes / 1024 / 1024).toStringAsFixed(2)}MB)');
      }
    }
  }

  /// 内存使用装饰器
  static T memoryWrapper<T>(String objectName, T Function() creator) {
    if (kDebugMode) {
      LoggerUtil.d('创建对象: $objectName');

      try {
        final result = creator();

        // 如果是控制器，记录创建
        if (objectName.toLowerCase().contains('controller')) {
          recordControllerCreation(objectName);
        }

        return result;
      } catch (e) {
        LoggerUtil.e('创建对象失败: $objectName, 错误: $e');
        rethrow;
      }
    } else {
      return creator();
    }
  }

  /// 启动内存监控
  static void startMemoryMonitoring() {
    if (kDebugMode) {
      // 定期检查内存状态
      Future.doWhile(() async {
        await Future.delayed(const Duration(minutes: 5));
        checkLongLivedControllers();
        _cleanupExpiredRecords();
        return true;
      });

      LoggerUtil.i('💾 内存监控已启动');
    }
  }
}
