import 'package:flutter/foundation.dart';
import 'logger_util.dart';

/// 代码优化工具
/// 提供代码质量检查和性能优化建议
class CodeOptimizer {
  static final Map<String, int> _methodCallCounts = {};
  static final Map<String, Duration> _methodExecutionTimes = {};
  static final List<String> _optimizationSuggestions = [];

  /// 记录方法调用
  static void recordMethodCall(String methodName) {
    if (kDebugMode) {
      _methodCallCounts[methodName] = (_methodCallCounts[methodName] ?? 0) + 1;
    }
  }

  /// 记录方法执行时间
  static void recordExecutionTime(String methodName, Duration duration) {
    if (kDebugMode) {
      _methodExecutionTimes[methodName] = duration;
      
      // 检查是否有性能问题
      if (duration.inMilliseconds > 100) {
        _optimizationSuggestions.add(
          '⚠️ 方法 $methodName 执行时间过长: ${duration.inMilliseconds}ms，建议优化'
        );
      }
    }
  }

  /// 检查频繁调用的方法
  static void checkFrequentCalls() {
    if (kDebugMode) {
      final frequentMethods = _methodCallCounts.entries
          .where((entry) => entry.value > 10)
          .toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      for (final entry in frequentMethods) {
        _optimizationSuggestions.add(
          '🔄 方法 ${entry.key} 被频繁调用 ${entry.value} 次，考虑添加缓存或优化调用逻辑'
        );
      }
    }
  }

  /// 生成优化报告
  static void generateOptimizationReport() {
    if (kDebugMode) {
      LoggerUtil.i('🔧 ========== 代码优化报告 ==========');
      
      if (_optimizationSuggestions.isEmpty) {
        LoggerUtil.i('✅ 暂无优化建议');
      } else {
        LoggerUtil.i('📋 优化建议:');
        for (final suggestion in _optimizationSuggestions) {
          LoggerUtil.i(suggestion);
        }
      }

      // 显示方法调用统计
      if (_methodCallCounts.isNotEmpty) {
        LoggerUtil.i('📊 方法调用统计:');
        final sortedCalls = _methodCallCounts.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));
        
        for (final entry in sortedCalls.take(10)) {
          LoggerUtil.i('  ${entry.key}: ${entry.value} 次');
        }
      }

      // 显示执行时间统计
      if (_methodExecutionTimes.isNotEmpty) {
        LoggerUtil.i('⏱️ 执行时间统计:');
        final sortedTimes = _methodExecutionTimes.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));
        
        for (final entry in sortedTimes.take(10)) {
          LoggerUtil.i('  ${entry.key}: ${entry.value.inMilliseconds}ms');
        }
      }

      LoggerUtil.i('🔧 ================================');
    }
  }

  /// 清理统计数据
  static void clearStatistics() {
    _methodCallCounts.clear();
    _methodExecutionTimes.clear();
    _optimizationSuggestions.clear();
  }

  /// 检查内存使用情况
  static void checkMemoryUsage() {
    if (kDebugMode) {
      // 这里可以添加内存使用检查逻辑
      LoggerUtil.i('💾 内存使用检查功能待实现');
    }
  }

  /// 检查数据库查询优化
  static void checkDatabaseQueries() {
    if (kDebugMode) {
      final dbMethods = _methodCallCounts.entries
          .where((entry) => entry.key.contains('query') || entry.key.contains('find'))
          .toList();

      if (dbMethods.isNotEmpty) {
        LoggerUtil.i('🗄️ 数据库查询统计:');
        for (final entry in dbMethods) {
          LoggerUtil.i('  ${entry.key}: ${entry.value} 次');
          if (entry.value > 5) {
            _optimizationSuggestions.add(
              '🗄️ 数据库查询 ${entry.key} 被频繁调用，建议添加缓存'
            );
          }
        }
      }
    }
  }

  /// 自动优化建议
  static List<String> getOptimizationSuggestions() {
    checkFrequentCalls();
    checkDatabaseQueries();
    return List.from(_optimizationSuggestions);
  }

  /// 性能监控装饰器
  static T performanceWrapper<T>(String methodName, T Function() method) {
    if (kDebugMode) {
      recordMethodCall(methodName);
      final stopwatch = Stopwatch()..start();
      
      try {
        final result = method();
        stopwatch.stop();
        recordExecutionTime(methodName, stopwatch.elapsed);
        return result;
      } catch (e) {
        stopwatch.stop();
        LoggerUtil.e('方法 $methodName 执行失败: $e');
        rethrow;
      }
    } else {
      return method();
    }
  }

  /// 异步性能监控装饰器
  static Future<T> asyncPerformanceWrapper<T>(
    String methodName, 
    Future<T> Function() method
  ) async {
    if (kDebugMode) {
      recordMethodCall(methodName);
      final stopwatch = Stopwatch()..start();
      
      try {
        final result = await method();
        stopwatch.stop();
        recordExecutionTime(methodName, stopwatch.elapsed);
        return result;
      } catch (e) {
        stopwatch.stop();
        LoggerUtil.e('异步方法 $methodName 执行失败: $e');
        rethrow;
      }
    } else {
      return await method();
    }
  }
}
