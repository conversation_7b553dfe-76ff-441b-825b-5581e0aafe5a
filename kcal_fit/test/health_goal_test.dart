import 'package:flutter_test/flutter_test.dart';
import 'package:kcal_fit/app/data/models/health_goal.dart';
import 'package:kcal_fit/app/data/services/sqflite/dao/impl/health_goal_dao.dart';

void main() {
  group('HealthGoal Tests', () {
    test('HealthGoal model creation and serialization', () {
      // 创建健康目标对象
      final healthGoal = HealthGoal(
        recordDate: DateTime.now(),
        goalTypes: '减重,增肌',
        targetWeight: 65.0,
        targetDays: 90,
        activityLevel: '中度活动',
      );

      // 测试对象创建
      expect(healthGoal.goalTypes, '减重,增肌');
      expect(healthGoal.targetWeight, 65.0);
      expect(healthGoal.targetDays, 90);
      expect(healthGoal.activityLevel, '中度活动');

      // 测试序列化
      final map = healthGoal.toMap();
      expect(map['goal_types'], '减重,增肌');
      expect(map['target_weight'], 65.0);
      expect(map['target_days'], 90);
      expect(map['activity_level'], '中度活动');

      // 测试反序列化
      final healthGoalFromMap = HealthGoal.fromMap(map);
      expect(healthGoalFromMap.goalTypes, healthGoal.goalTypes);
      expect(healthGoalFromMap.targetWeight, healthGoal.targetWeight);
      expect(healthGoalFromMap.targetDays, healthGoal.targetDays);
      expect(healthGoalFromMap.activityLevel, healthGoal.activityLevel);
    });

    test('HealthGoal copyWith method', () {
      final originalGoal = HealthGoal(
        recordDate: DateTime.now(),
        goalTypes: '减重',
        targetWeight: 60.0,
        targetDays: 60,
        activityLevel: '轻度活动',
      );

      final updatedGoal = originalGoal.copyWith(
        targetWeight: 65.0,
        targetDays: 90,
      );

      expect(updatedGoal.goalTypes, '减重'); // 保持不变
      expect(updatedGoal.targetWeight, 65.0); // 已更新
      expect(updatedGoal.targetDays, 90); // 已更新
      expect(updatedGoal.activityLevel, '轻度活动'); // 保持不变
    });

    test('HealthGoalDao basic operations', () {
      // 注意：这个测试需要数据库环境，在实际项目中可能需要mock
      final dao = HealthGoalDao();
      
      // 测试DAO基本方法存在
      expect(dao.tableName, isNotEmpty);
      expect(dao.toMap, isA<Function>());
      expect(dao.fromMap, isA<Function>());
      expect(dao.getId, isA<Function>());
      expect(dao.setId, isA<Function>());
    });
  });
}
