import 'package:flutter_test/flutter_test.dart';
import 'package:kcal_fit/app/data/models/health_goal.dart';
import 'package:kcal_fit/app/data/services/sqflite/dao/impl/health_goal_dao.dart';
import 'package:kcal_fit/app/modules/goal_setting/controllers/goal_setting_controller.dart';

void main() {
  group('健康目标存储和回显测试', () {
    late HealthGoalDao healthGoalDao;

    setUp(() {
      healthGoalDao = HealthGoalDao();
    });

    test('健康目标数据库存储和查询测试', () async {
      // 创建测试数据
      final testGoal = HealthGoal(recordDate: DateTime.now(), goalTypes: '减重,增肌', targetWeight: 65.0, targetDays: 90);

      // 测试数据验证
      expect(testGoal.goalTypes, isNotNull);

      // 注意：这个测试需要实际的数据库环境
      // 在实际项目中，你可能需要设置测试数据库或使用mock

      // 验证DAO方法存在且可调用
      expect(healthGoalDao.tableName, isNotEmpty);
      expect(healthGoalDao.toMap(testGoal), isA<Map<String, dynamic>>());

      // 验证序列化和反序列化
      final map = healthGoalDao.toMap(testGoal);
      final reconstructed = healthGoalDao.fromMap(map);

      expect(reconstructed.goalTypes, testGoal.goalTypes);
      expect(reconstructed.targetWeight, testGoal.targetWeight);
      expect(reconstructed.targetDays, testGoal.targetDays);
    });

    test('健康目标数据模型验证', () {
      final goal = HealthGoal(recordDate: DateTime.now(), goalTypes: '减重', targetWeight: 60.0, targetDays: 60);

      // 测试copyWith方法
      final updatedGoal = goal.copyWith(targetWeight: 65.0, targetDays: 90);

      expect(updatedGoal.goalTypes, '减重'); // 保持不变
      expect(updatedGoal.targetWeight, 65.0); // 已更新
      expect(updatedGoal.targetDays, 90); // 已更新

      // 测试toString方法
      expect(updatedGoal.toString(), contains('HealthGoal'));
      expect(updatedGoal.toString(), contains('减重'));
      expect(updatedGoal.toString(), contains('65.0'));
      expect(updatedGoal.toString(), contains('90'));
    });

    test('目标期限选项一致性测试', () {
      final controller = GoalSettingController();

      // 验证目标期限选项与引导页一致
      expect(controller.targetDaysOptions.length, 4);

      // 验证选项内容
      final options = controller.targetDaysOptions;
      expect(options[0]['label'], '1个月');
      expect(options[0]['days'], 30);
      expect(options[1]['label'], '3个月');
      expect(options[1]['days'], 90);
      expect(options[2]['label'], '6个月');
      expect(options[2]['days'], 180);
      expect(options[3]['label'], '1年');
      expect(options[3]['days'], 365);

      // 测试标签获取方法
      controller.updateTargetDays(30);
      expect(controller.getTargetDaysLabel(), '1个月');

      controller.updateTargetDays(90);
      expect(controller.getTargetDaysLabel(), '3个月');

      controller.updateTargetDays(180);
      expect(controller.getTargetDaysLabel(), '6个月');

      controller.updateTargetDays(365);
      expect(controller.getTargetDaysLabel(), '1年');

      // 测试未知天数的默认显示
      controller.updateTargetDays(45);
      expect(controller.getTargetDaysLabel(), '45天');
    });
  });
}
